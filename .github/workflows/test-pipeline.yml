name: BenefitLens Test Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

permissions:
  contents: read
  actions: read

env:
  NODE_VERSION: '18'
  DATABASE_URL: ${{ secrets.TEST_DATABASE_URL }}
  NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}
  NEXTAUTH_URL: http://localhost:3000

jobs:
  # Job 1: Unit Tests
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run TypeScript check
      run: npx tsc --noEmit
      
    - name: Run ESLint
      run: npm run lint
      
    - name: Run unit tests
      run: npm run test:unit -- --run --coverage --reporter=json --outputFile=unit-results.json
      
    - name: Upload unit test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: unit-test-results
        path: |
          unit-results.json
          coverage/
          
    - name: Comment PR with unit test results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          if (fs.existsSync('unit-results.json')) {
            const results = JSON.parse(fs.readFileSync('unit-results.json', 'utf8'));
            const comment = `## 🧪 Unit Test Results
            
            - **Tests**: ${results.numTotalTests}
            - **Passed**: ${results.numPassedTests}
            - **Failed**: ${results.numFailedTests}
            - **Duration**: ${results.testResults?.[0]?.perfStats?.runtime || 'N/A'}ms
            
            ${results.numFailedTests > 0 ? '❌ Some unit tests failed' : '✅ All unit tests passed'}`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          }

  # Job 2: Integration Tests
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: benefitlens
          POSTGRES_USER: benefitlens_user
          POSTGRES_PASSWORD: benefitlens_password
        ports:
          - 5432:5432
        options: >-
          --health-cmd="pg_isready -U benefitlens_user -d benefitlens"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=10
          
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Initialize and seed test database
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password
      run: |
        psql -h localhost -U benefitlens_user -d benefitlens -f database/init/01-init.sql
        psql -h localhost -U benefitlens_user -d benefitlens -f database/seed.sql

    - name: Build application
      run: npm run build
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password
    - name: Run integration tests
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password
        NEXT_PUBLIC_APP_URL: http://localhost:3000
        APP_URL: http://localhost:3000
        LOG_LEVEL: warn
        USE_LOCAL_AUTH: true
        SESSION_SECRET: test-session-secret-for-ci-integration-tests
        CACHE_TYPE: postgresql
      run: npm run test:integration -- --run --reporter=json --outputFile=integration-results.json
      
    - name: Upload integration test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: integration-test-results
        path: integration-results.json

  # Job 3a: E2E Tests - Split into multiple parallel jobs
  e2e-essential:
    name: E2E Essential & User Journey Tests
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: benefitlens
          POSTGRES_USER: benefitlens_user
          POSTGRES_PASSWORD: benefitlens_password
        ports:
          - 5432:5432
        options: >-
          --health-cmd="pg_isready -U benefitlens_user -d benefitlens"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=10
          
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Install Playwright browsers
      run: npx playwright install --with-deps
      
    - name: Initialize and seed test database
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password
      run: |
        psql -h localhost -U benefitlens_user -d benefitlens -f database/init/01-init.sql
        psql -h localhost -U benefitlens_user -d benefitlens -f database/seed.sql
        
    - name: Build application
      run: npm run build
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password
      
    - name: Run Essential & User Journey E2E tests
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password
        E2E_BASE_URL: http://localhost:3000
      run: npx playwright test --config=playwright-conservative.config.ts src/__tests__/e2e/essential-tests.spec.ts src/__tests__/e2e/user-journeys.spec.ts
      
    - name: Upload E2E test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: e2e-test-results
        path: |
          test-results/
          playwright-report/
          
    - name: Upload E2E artifacts
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: e2e-essential-artifacts
        path: |
          test-results/e2e-artifacts/

  # Job 3b: E2E Admin Tests
  e2e-admin:
    name: E2E Admin Tests
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: benefitlens
          POSTGRES_USER: benefitlens_user
          POSTGRES_PASSWORD: benefitlens_password
        ports:
          - 5432:5432
        options: >-
          --health-cmd="pg_isready -U benefitlens_user -d benefitlens"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=10

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright browsers
      run: npx playwright install --with-deps

    - name: Initialize and seed test database
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password
      run: |
        psql -h localhost -U benefitlens_user -d benefitlens -f database/init/01-init.sql
        psql -h localhost -U benefitlens_user -d benefitlens -f database/seed.sql

    - name: Build application
      run: npm run build
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password

    - name: Run Admin E2E tests
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password
        E2E_BASE_URL: http://localhost:3000
      run: npx playwright test --config=playwright-conservative.config.ts src/__tests__/e2e/admin-*.spec.ts

    - name: Upload E2E test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: e2e-admin-test-results
        path: |
          test-results/
          playwright-report/

    - name: Upload E2E artifacts
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: e2e-admin-artifacts
        path: |
          test-results/e2e-artifacts/

  # Job 3b: E2E Privacy Journey Tests
  e2e-privacy:
    name: E2E Privacy Journey Tests
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: benefitlens
          POSTGRES_USER: benefitlens_user
          POSTGRES_PASSWORD: benefitlens_password
        ports:
          - 5432:5432
        options: >-
          --health-cmd="pg_isready -U benefitlens_user -d benefitlens"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=10

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright browsers
      run: npx playwright install --with-deps

    - name: Initialize and seed test database
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password
      run: |
        psql -h localhost -U benefitlens_user -d benefitlens -f database/init/01-init.sql
        psql -h localhost -U benefitlens_user -d benefitlens -f database/seed.sql

    - name: Build application
      run: npm run build
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password

    - name: Run Privacy Journey E2E tests
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password
        E2E_BASE_URL: http://localhost:3000
      run: npx playwright test --config=playwright-conservative.config.ts src/__tests__/e2e/privacy-*.spec.ts

    - name: Upload E2E test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: e2e-privacy-test-results
        path: |
          test-results/
          playwright-report/

    - name: Upload E2E artifacts
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: e2e-privacy-artifacts
        path: |
          test-results/e2e-artifacts/

  # Job 3d: E2E Mobile & Device Tests
  e2e-mobile:
    name: E2E Mobile & Device Tests
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: benefitlens
          POSTGRES_USER: benefitlens_user
          POSTGRES_PASSWORD: benefitlens_password
        ports:
          - 5432:5432
        options: >-
          --health-cmd="pg_isready -U benefitlens_user -d benefitlens"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=10

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright browsers
      run: npx playwright install --with-deps

    - name: Initialize and seed test database
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password
      run: |
        psql -h localhost -U benefitlens_user -d benefitlens -f database/init/01-init.sql
        psql -h localhost -U benefitlens_user -d benefitlens -f database/seed.sql

    - name: Build application
      run: npm run build
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password

    - name: Run Mobile & Device E2E tests
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password
        E2E_BASE_URL: http://localhost:3000
      run: npx playwright test --config=playwright-conservative.config.ts src/__tests__/e2e/mobile-*.spec.ts src/__tests__/e2e/device-*.spec.ts src/__tests__/e2e/button-design-consistency.spec.ts

    - name: Upload E2E test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: e2e-mobile-test-results
        path: |
          test-results/
          playwright-report/

    - name: Upload E2E artifacts
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: e2e-mobile-artifacts
        path: |
          test-results/e2e-artifacts/

  # Job 3e: E2E Accessibility & Benefit Dispute Journey Tests
  e2e-accessibility:
    name: E2E Accessibility & Benefit Dispute Journey Tests
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: benefitlens
          POSTGRES_USER: benefitlens_user
          POSTGRES_PASSWORD: benefitlens_password
        ports:
          - 5432:5432
        options: >-
          --health-cmd="pg_isready -U benefitlens_user -d benefitlens"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=10

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright browsers
      run: npx playwright install --with-deps

    - name: Initialize and seed test database
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password
      run: |
        psql -h localhost -U benefitlens_user -d benefitlens -f database/init/01-init.sql
        psql -h localhost -U benefitlens_user -d benefitlens -f database/seed.sql

    - name: Build application
      run: npm run build
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password

    - name: Run Accessibility & Benefit Dispute Journey E2E tests
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password
        E2E_BASE_URL: http://localhost:3000
      run: npx playwright test --config=playwright-conservative.config.ts src/__tests__/e2e/accessibility-*.spec.ts src/__tests__/e2e/benefit-*.spec.ts

    - name: Upload E2E test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: e2e-accessibility-test-results
        path: |
          test-results/
          playwright-report/

    - name: Upload E2E artifacts
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: e2e-accessibility-artifacts
        path: |
          test-results/e2e-artifacts/

  # Job 4: Security Tests
  security-tests:
    name: Security Tests
    runs-on: ubuntu-latest
    permissions:
      contents: read
      actions: read

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run npm audit
      run: |
        echo "## 🔒 Security Audit Results" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        # Run npm audit and capture output
        if npm audit --audit-level=moderate; then
          echo "✅ **No security vulnerabilities found**" >> $GITHUB_STEP_SUMMARY
        else
          echo "⚠️ **Security vulnerabilities detected**" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "Please review the audit results above and update dependencies as needed." >> $GITHUB_STEP_SUMMARY
        fi
      continue-on-error: true

    - name: Check for outdated dependencies
      run: |
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "## 📦 Dependency Status" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        # Check for outdated packages
        if npm outdated; then
          echo "⚠️ **Some dependencies are outdated**" >> $GITHUB_STEP_SUMMARY
        else
          echo "✅ **All dependencies are up to date**" >> $GITHUB_STEP_SUMMARY
        fi
      continue-on-error: true

  # Job 5: Performance Tests
  # performance-tests:
  #   name: Performance Tests
  #   runs-on: ubuntu-latest
  #   needs: [unit-tests, integration-tests]
    
  #   steps:
  #   - name: Checkout code
  #     uses: actions/checkout@v4
      
  #   - name: Setup Node.js
  #     uses: actions/setup-node@v4
  #     with:
  #       node-version: ${{ env.NODE_VERSION }}
  #       cache: 'npm'
        
  #   - name: Install dependencies
  #     run: npm ci
      
  #   - name: Build application
  #     env:
  #       BUILD_PHASE: true
  #     run: npm run build
      
  #   - name: Run Lighthouse CI
  #     run: |
  #       npm install -g @lhci/cli@0.12.x
  #       lhci autorun
  #     env:
  #       LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  # Job 6: Test Summary
  test-summary:
    name: Test Summary
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, e2e-essential, e2e-admin, e2e-privacy, e2e-mobile, e2e-accessibility, security-tests]
    if: always()
    
    steps:
    - name: Download all test results
      uses: actions/download-artifact@v4
      
    - name: Generate test summary
      run: |
        echo "# 🧪 BenefitLens Test Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        # Unit Tests
        if [ -f "unit-test-results/unit-results.json" ]; then
          echo "## Unit Tests ✅" >> $GITHUB_STEP_SUMMARY
          echo "- Status: Passed" >> $GITHUB_STEP_SUMMARY
        else
          echo "## Unit Tests ❌" >> $GITHUB_STEP_SUMMARY
          echo "- Status: Failed" >> $GITHUB_STEP_SUMMARY
        fi
        echo "" >> $GITHUB_STEP_SUMMARY
        
        # Integration Tests
        if [ -f "integration-test-results/integration-results.json" ]; then
          echo "## Integration Tests ✅" >> $GITHUB_STEP_SUMMARY
          echo "- Status: Passed" >> $GITHUB_STEP_SUMMARY
        else
          echo "## Integration Tests ❌" >> $GITHUB_STEP_SUMMARY
          echo "- Status: Failed" >> $GITHUB_STEP_SUMMARY
        fi
        echo "" >> $GITHUB_STEP_SUMMARY
        
        # E2E Tests
        e2e_status="✅"
        if [ ! -d "e2e-essential-test-results" ]; then
          e2e_status="❌"
        fi
        if [ ! -d "e2e-admin-test-results" ]; then
          e2e_status="❌"
        fi
        if [ ! -d "e2e-privacy-test-results" ]; then
          e2e_status="❌"
        fi
        if [ ! -d "e2e-mobile-test-results" ]; then
          e2e_status="❌"
        fi
        if [ ! -d "e2e-accessibility-test-results" ]; then
          e2e_status="❌"
        fi

        echo "## E2E Tests $e2e_status" >> $GITHUB_STEP_SUMMARY
        echo "- Essential Tests: $([ -d "e2e-essential-test-results" ] && echo "✅" || echo "❌")" >> $GITHUB_STEP_SUMMARY
        echo "- Admin Tests: $([ -d "e2e-admin-test-results" ] && echo "✅" || echo "❌")" >> $GITHUB_STEP_SUMMARY
        echo "- Privacy Tests: $([ -d "e2e-privacy-test-results" ] && echo "✅" || echo "❌")" >> $GITHUB_STEP_SUMMARY
        echo "- Mobile Tests: $([ -d "e2e-mobile-test-results" ] && echo "✅" || echo "❌")" >> $GITHUB_STEP_SUMMARY
        echo "- Accessibility Tests: $([ -d "e2e-accessibility-test-results" ] && echo "✅" || echo "❌")" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        # Overall Status
        if [ "${{ needs.unit-tests.result }}" == "success" ] && \
           [ "${{ needs.integration-tests.result }}" == "success" ] && \
           [ "${{ needs.e2e-essential.result }}" == "success" ] && \
           [ "${{ needs.e2e-admin.result }}" == "success" ] && \
           [ "${{ needs.e2e-privacy.result }}" == "success" ] && \
           [ "${{ needs.e2e-mobile.result }}" == "success" ] && \
           [ "${{ needs.e2e-accessibility.result }}" == "success" ] && \
           [ "${{ needs.security-tests.result }}" == "success" ]; then
          echo "## 🎉 Overall Status: READY FOR PRODUCTION" >> $GITHUB_STEP_SUMMARY
        else
          echo "## ❌ Overall Status: NOT READY FOR PRODUCTION" >> $GITHUB_STEP_SUMMARY
        fi
        
    - name: Set deployment status
      if: github.ref == 'refs/heads/main'
      run: |
        if [ "${{ needs.unit-tests.result }}" == "success" ] && \
           [ "${{ needs.integration-tests.result }}" == "success" ] && \
           [ "${{ needs.e2e-essential.result }}" == "success" ] && \
           [ "${{ needs.e2e-admin.result }}" == "success" ] && \
           [ "${{ needs.e2e-privacy.result }}" == "success" ] && \
           [ "${{ needs.e2e-mobile.result }}" == "success" ] && \
           [ "${{ needs.e2e-accessibility.result }}" == "success" ] && \
           [ "${{ needs.security-tests.result }}" == "success" ]; then
          echo "DEPLOY_READY=true" >> $GITHUB_ENV
        else
          echo "DEPLOY_READY=false" >> $GITHUB_ENV
        fi
