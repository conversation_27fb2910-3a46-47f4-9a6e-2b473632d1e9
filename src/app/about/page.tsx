import { Header } from '@/components/header'
import { CheckCircle, Search, Shield, Users } from 'lucide-react'

export default function AboutPage() {
  return (
    <div className="bg-gray-50">
      <Header />
      <main className="container mx-auto px-4 py-6 sm:py-8">
        <div className="max-w-4xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-10 sm:mb-16">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-2">
              About BenefitLens
            </h1>
            <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto px-2">
              We&apos;re making it easier for job seekers to find companies that truly care about their employees&apos; well-being.
            </p>
          </div>

          {/* Mission Section */}
          <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6 lg:p-8 mb-8 sm:mb-12">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4">Our Mission</h2>
            <p className="text-sm sm:text-base text-gray-600 leading-relaxed mb-4 sm:mb-6">
              In today&apos;s competitive job market, employee benefits have become a crucial factor in career decisions.
              However, finding comprehensive and accurate information about company benefits has always been challenging.
              BenefitLens bridges this gap by providing a transparent platform where job seekers can discover and compare
              companies based on their actual employee benefits.
            </p>
            <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
              We believe that transparency in benefits leads to better matches between employers and employees,
              ultimately creating more satisfied workforces and stronger companies.
            </p>
          </div>

          {/* Features Section */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 mb-8 sm:mb-12">
            <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6">
              <div className="flex items-center mb-3 sm:mb-4">
                <Search className="w-6 h-6 sm:w-8 sm:h-8 text-blue-600 mr-2 sm:mr-3 flex-shrink-0" />
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900">Smart Search</h3>
              </div>
              <p className="text-sm sm:text-base text-gray-600">
                Find companies by specific benefits, location, size, and industry.
                Search for exactly what matters to you, like &ldquo;Wellpass&rdquo; or &ldquo;sabbatical leave&rdquo;.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6">
              <div className="flex items-center mb-3 sm:mb-4">
                <Shield className="w-6 h-6 sm:w-8 sm:h-8 text-green-600 mr-2 sm:mr-3 flex-shrink-0" />
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900">Verified Information</h3>
              </div>
              <p className="text-sm sm:text-base text-gray-600">
                Company representatives can claim and verify their profiles, ensuring
                the benefits information is accurate and up-to-date.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6">
              <div className="flex items-center mb-3 sm:mb-4">
                <Users className="w-6 h-6 sm:w-8 sm:h-8 text-purple-600 mr-2 sm:mr-3 flex-shrink-0" />
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900">Community Driven</h3>
              </div>
              <p className="text-sm sm:text-base text-gray-600">
                Employees can contribute to benefit information, creating a comprehensive
                and community-verified database of company benefits.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6">
              <div className="flex items-center mb-3 sm:mb-4">
                <CheckCircle className="w-6 h-6 sm:w-8 sm:h-8 text-orange-600 mr-2 sm:mr-3 flex-shrink-0" />
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900">Easy Comparison</h3>
              </div>
              <p className="text-sm sm:text-base text-gray-600">
                Compare multiple companies side by side to make informed decisions
                about your next career move based on what matters most to you.
              </p>
            </div>
          </div>

          {/* How It Works Section */}
          <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6 lg:p-8 mb-8 sm:mb-12">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6">How It Works</h2>
            <div className="space-y-4 sm:space-y-6">
              <div className="flex items-start space-x-3 sm:space-x-4">
                <div className="w-7 h-7 sm:w-8 sm:h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-blue-600 font-semibold text-sm sm:text-base">1</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1 sm:mb-2 text-sm sm:text-base">Search & Discover</h3>
                  <p className="text-gray-600 text-sm sm:text-base">
                    Use our search filters to find companies that offer the benefits you care about most.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3 sm:space-x-4">
                <div className="w-7 h-7 sm:w-8 sm:h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-blue-600 font-semibold text-sm sm:text-base">2</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1 sm:mb-2 text-sm sm:text-base">Compare Benefits</h3>
                  <p className="text-gray-600 text-sm sm:text-base">
                    View detailed benefit information for each company, including verification status.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3 sm:space-x-4">
                <div className="w-7 h-7 sm:w-8 sm:h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-blue-600 font-semibold text-sm sm:text-base">3</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1 sm:mb-2 text-sm sm:text-base">Verify & Contribute</h3>
                  <p className="text-gray-600 text-sm sm:text-base">
                    Employees can verify benefit information for their companies, helping build a trusted community database.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3 sm:space-x-4">
                <div className="w-7 h-7 sm:w-8 sm:h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-blue-600 font-semibold text-sm sm:text-base">4</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1 sm:mb-2 text-sm sm:text-base">Make Informed Decisions</h3>
                  <p className="text-gray-600 text-sm sm:text-base">
                    Choose companies that align with your values and benefit preferences based on verified information.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Verification Process Section */}
          <div id="verification" className="bg-white rounded-lg shadow-sm border p-4 sm:p-6 lg:p-8 mb-8 sm:mb-12">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6">Benefit Verification Process</h2>

            <div className="mb-6 sm:mb-8">
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">Understanding Verification Status</h3>
              <div className="grid gap-4 sm:gap-6 md:grid-cols-2">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                    <h4 className="font-semibold text-green-800">Verified Benefits</h4>
                  </div>
                  <p className="text-sm text-green-700">
                    Benefits confirmed by at least 2 employees of the company. These have been validated by people who actually work there.
                  </p>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <Shield className="w-5 h-5 text-yellow-600 mr-2" />
                    <h4 className="font-semibold text-yellow-800">Unverified Benefits</h4>
                  </div>
                  <p className="text-sm text-yellow-700">
                    Benefits that need employee confirmation. They may be accurate but haven't been verified yet by enough employees.
                  </p>
                </div>
              </div>
            </div>

            <div className="mb-6 sm:mb-8">
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">How to Verify Benefits</h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-blue-600 font-semibold text-xs">1</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Sign in with your company email</h4>
                    <p className="text-sm text-gray-600">
                      Use your work email address to automatically associate with your company. We verify your domain matches the company.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-blue-600 font-semibold text-xs">2</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Review your company's benefits</h4>
                    <p className="text-sm text-gray-600">
                      Check the listed benefits and confirm which ones your company actually offers.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-blue-600 font-semibold text-xs">3</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Confirm or dispute benefits</h4>
                    <p className="text-sm text-gray-600">
                      Click "Confirm" for accurate benefits or "Dispute" for incorrect ones. You can also add missing benefits.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-blue-900 mb-2">Help Improve Data Accuracy</h3>
              <p className="text-sm text-blue-800 mb-3">
                Your contributions help build a more accurate and comprehensive database for everyone:
              </p>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Verify benefits at your current or former companies</li>
                <li>• Add missing benefits that aren't listed</li>
                <li>• Report outdated or incorrect information</li>
                <li>• Help job seekers make informed decisions</li>
              </ul>
            </div>
          </div>

          {/* Contact Section */}
          <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6 lg:p-8 text-center">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4">Get In Touch</h2>
            <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6 px-2">
              Have questions or want to add your company to our platform? We&apos;d love to hear from you.
            </p>
            <div className="space-y-2">
              <p className="text-sm sm:text-base text-gray-600">
                <strong>Email:</strong> <EMAIL>
              </p>
              <p className="text-sm sm:text-base text-gray-600">
                <strong>For Companies:</strong> <EMAIL>
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
