import type { <PERSON>ada<PERSON>, Viewport } from "next";
import "./globals.css";
import { Footer } from "@/components/footer";
import { EnhancedCookieBanner } from "@/components/enhanced-cookie-banner";
import { WebsiteStructuredData } from "@/components/structured-data";

// Initialize performance monitoring and environment validation
import { startPerformanceMonitoring } from "@/lib/performance";
import { validateEnvironment } from "@/lib/env-validation";

// Initialize systems on server startup (skip during build phase)
if (typeof window === 'undefined' && process.env.BUILD_PHASE !== 'true') {
  // Validate environment
  validateEnvironment();

  // Start performance monitoring
  startPerformanceMonitoring();
}

export const metadata: Metadata = {
  title: {
    default: "BenefitLens - Compare Companies by Benefits",
    template: "%s | BenefitLens"
  },
  description: "Find and compare companies based on their employee benefits. Search by location, benefits, and more. Discover companies offering Wellpass, sabbatical leave, remote work, and other benefits.",
  keywords: [
    "employee benefits",
    "company benefits",
    "Wellpass",
    "sabbatical leave",
    "remote work",
    "company comparison",
    "benefits search",
    "employer benefits",
    "workplace benefits",
    "German companies",
    "benefits finder"
  ],
  authors: [{ name: "BenefitLens" }],
  creator: "BenefitLens",
  publisher: "BenefitLens",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://benefitlens.de'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "BenefitLens - Compare Companies by Benefits",
    description: "Find and compare companies based on their employee benefits. Search by location, benefits, and more.",
    url: 'https://benefitlens.de',
    siteName: 'BenefitLens',
    locale: 'en_US',
    type: 'website',
    images: [
      {
        url: '/og-image.svg',
        width: 1200,
        height: 630,
        alt: 'BenefitLens - Compare Companies by Benefits',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: "BenefitLens - Compare Companies by Benefits",
    description: "Find and compare companies based on their employee benefits. Search by location, benefits, and more.",
    images: ['/og-image.svg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="antialiased bg-gray-50">
        <div className="min-h-screen flex flex-col">
          <div className="flex-1">
            {children}
          </div>
          <Footer />
        </div>
        <EnhancedCookieBanner />
        <WebsiteStructuredData />
      </body>
    </html>
  );
}
